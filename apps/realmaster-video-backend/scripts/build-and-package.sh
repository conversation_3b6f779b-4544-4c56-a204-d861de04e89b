#!/bin/bash

# RealMaster Video Backend - Build and Package Script
# This script builds the Go binaries and creates Podman images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_IMAGE_NAME="realmaster-video-api"
WORKER_IMAGE_NAME="realmaster-video-worker"
IMAGE_TAG="${IMAGE_TAG:-latest}"
BUILD_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🚀 RealMaster Video Backend - Build and Package${NC}"
echo -e "${BLUE}=================================================${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if we're in the right directory
if [[ ! -f "go.mod" ]]; then
    print_error "Please run this script from the realmaster-video-backend directory"
    exit 1
fi

# Check if Podman is installed
if ! command -v podman &> /dev/null; then
    print_error "Podman is not installed. Please install Podman first."
    exit 1
fi

# Clean up any existing binaries
print_status "Cleaning up existing binaries..."
rm -f server worker

# Build API server binary
print_status "Building API server binary..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o server ./cmd/api/main.go

if [[ ! -f "server" ]]; then
    print_error "Failed to build API server binary"
    exit 1
fi

# Build Worker binary
print_status "Building Worker binary..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-w -s" -o worker ./cmd/video-worker/main.go

if [[ ! -f "worker" ]]; then
    print_error "Failed to build Worker binary"
    exit 1
fi

# Build API server image
print_status "Building API server Podman image..."
podman build -f Dockerfile.api -t "${API_IMAGE_NAME}:${IMAGE_TAG}" .

# Build Worker image
print_status "Building Worker Podman image..."
podman build -f Dockerfile.worker -t "${WORKER_IMAGE_NAME}:${IMAGE_TAG}" .

# Clean up binaries
print_status "Cleaning up temporary binaries..."
rm -f server worker

# Show built images
print_status "Built images:"
podman images | grep -E "(${API_IMAGE_NAME}|${WORKER_IMAGE_NAME})" | head -2

echo ""
echo -e "${GREEN}🎉 Build and packaging completed successfully!${NC}"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo -e "  1. Create podman-compose.yml (see README.md)"
echo -e "  2. Run: ${YELLOW}podman-compose up -d${NC}"
echo -e "  3. Check status: ${YELLOW}podman-compose ps${NC}"
echo ""
echo -e "${BLUE}To save images for deployment:${NC}"
echo -e "  ${YELLOW}podman save ${API_IMAGE_NAME}:${IMAGE_TAG} -o api-image.tar${NC}"
echo -e "  ${YELLOW}podman save ${WORKER_IMAGE_NAME}:${IMAGE_TAG} -o worker-image.tar${NC}"
echo ""
echo -e "${BLUE}To load images on target server:${NC}"
echo -e "  ${YELLOW}podman load -i api-image.tar${NC}"
echo -e "  ${YELLOW}podman load -i worker-image.tar${NC}"
