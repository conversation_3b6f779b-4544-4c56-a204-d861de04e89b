package testutil

import (
	"context"
	"os"
	"testing"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/platform/database"
	"realmaster-video-backend/internal/platform/logger"
)

// TestSuite 测试套件，管理测试环境的生命周期
type TestSuite struct {
	Config *config.Config
	T      *testing.T
}

// SetupTestSuite 初始化测试环境
// 这个函数会：
// 1. 加载测试配置
// 2. 初始化日志系统
// 3. 初始化数据库连接
func SetupTestSuite(t *testing.T) *TestSuite {
	// 获取当前工作目录，确保能找到配置文件
	wd, err := os.Getwd()
	if err != nil {
		t.Fatalf("获取工作目录失败: %v", err)
	}

	// 查找项目根目录（包含test_config.toml的目录）
	configPath := ""
	for {
		testConfigPath := wd + "/test_config.toml"
		if _, err := os.Stat(testConfigPath); err == nil {
			configPath = testConfigPath
			break
		}

		parent := wd + "/.."
		if parent == wd {
			t.Skip("跳过测试：找不到test_config.toml文件")
		}
		wd = parent
	}

	// 设置测试配置文件
	originalConfigFile := os.Getenv("CONFIG_FILE")
	originalRMBaseFile := os.Getenv("RMBASE_FILE_CFG")

	os.Setenv("CONFIG_FILE", configPath)
	os.Setenv("RMBASE_FILE_CFG", configPath)

	// 确保测试结束后恢复原始配置
	t.Cleanup(func() {
		if originalConfigFile != "" {
			os.Setenv("CONFIG_FILE", originalConfigFile)
		} else {
			os.Unsetenv("CONFIG_FILE")
		}

		if originalRMBaseFile != "" {
			os.Setenv("RMBASE_FILE_CFG", originalRMBaseFile)
		} else {
			os.Unsetenv("RMBASE_FILE_CFG")
		}
	})

	// 1. 加载测试配置
	cfg, err := config.LoadConfig()
	if err != nil {
		t.Fatalf("加载测试配置失败: %v", err)
	}

	// 2. 初始化日志系统（使用测试配置）
	if _, err := logger.NewLogger(); err != nil {
		t.Fatalf("初始化测试日志失败: %v", err)
	}

	// 3. 初始化数据库连接
	// gomongo会自动从test_config.toml读取数据库配置
	err = database.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：无法连接到测试数据库: %v\n请确保MongoDB服务器正在运行: mongodb://localhost:27017", err)
	}

	// 验证数据库连接是否真的可用
	collection := database.GetCollection("realmaster_video", "video_videos")
	if collection == nil {
		t.Skip("跳过测试：数据库集合初始化失败，请检查MongoDB连接")
	}

	return &TestSuite{
		Config: cfg,
		T:      t,
	}
}

// CleanupDatabase 清理测试数据库
// 在每个测试后调用，确保测试隔离
func (ts *TestSuite) CleanupDatabase() {
	ctx := context.Background()

	// 清理各个集合的测试数据
	collections := []struct {
		dbName         string
		collectionName string
	}{
		{"realmaster_video", "video_videos"},
		{"realmaster_video", "categories"},
		{"realmaster_video", "advertisers"},
		{"realmaster_video", "interactions"},
		{"realmaster_video", "states"},
	}

	for _, coll := range collections {
		collection := database.GetCollection(coll.dbName, coll.collectionName)
		if collection != nil {
			// 删除所有测试数据（可以根据需要添加过滤条件）
			_, err := collection.DeleteMany(ctx, map[string]interface{}{})
			if err != nil {
				ts.T.Logf("清理集合 %s.%s 失败: %v", coll.dbName, coll.collectionName, err)
			}
		}
	}
}

// CreateTempDirs 创建测试需要的临时目录
func (ts *TestSuite) CreateTempDirs() {
	dirs := []string{
		ts.Config.Server.DraftDir,
		ts.Config.Media.StorageDir,
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			ts.T.Fatalf("创建测试目录 %s 失败: %v", dir, err)
		}
	}

	// 确保测试结束后清理临时目录
	ts.T.Cleanup(func() {
		for _, dir := range dirs {
			os.RemoveAll(dir)
		}
	})
}
