package advertiser

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/logger"

	"github.com/google/uuid"
	"github.com/real-rm/gofile/levelStore"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ErrAdvertiserNotFound       = errors.New("广告主不存在")
	ErrInvalidAdvertiserID      = errors.New("无效的广告主ID")
	ErrEmailExists              = errors.New("电子邮箱已被使用")
	ErrPhoneExists              = errors.New("电话号码已被使用")
	ErrNameExists               = errors.New("广告主名称已被使用")
	ErrInvalidEmailFormat       = errors.New("无效的电子邮箱格式")
	ErrInvalidPhoneFormat       = errors.New("无效的电话号码格式")
	ErrCannotDeleteLast         = errors.New("无法删除唯一的广告主")
	ErrMergeToSelf              = errors.New("不能将广告主合并到其自身")
	ErrTargetAdvertiserNotFound = errors.New("目标广告主不存在")
)

// Service 定义了广告主相关的业务逻辑接口
type Service interface {
	List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error)
	GetByID(ctx context.Context, id string) (*Advertiser, error)
	Create(ctx context.Context, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error)
	Update(ctx context.Context, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error
	Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error
}

type service struct {
	repo      Repository
	videoRepo video.Repository
	cfg       *config.Config
}

// NewService 创建一个新的广告主服务实例
func NewService(repo Repository, videoRepo video.Repository, cfg *config.Config) Service {
	return &service{
		repo:      repo,
		videoRepo: videoRepo,
		cfg:       cfg,
	}
}

// List 获取广告主列表（带分页和筛选）
func (s *service) List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error) {
	// 1. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10 // 默认每页10条
	}

	// 2. 构建查询 filter
	filter := bson.M{}
	if req.Name != "" {
		// 使用 primitive.Regex 实现模糊查询
		filter["nm"] = primitive.Regex{Pattern: req.Name, Options: "i"} // i 表示不区分大小写
	}
	if req.Phone != "" {
		filter["ph"] = req.Phone
	}
	if req.Email != "" {
		filter["em"] = req.Email
	}

	// 3. 并行执行查询和计数
	var advertisers []Advertiser
	var total int64
	var findErr, countErr error

	errChan := make(chan error, 2)
	go func() {
		advertisers, findErr = s.repo.Find(ctx, filter, req.Page, req.Limit)
		errChan <- findErr
	}()
	go func() {
		total, countErr = s.repo.Count(ctx, filter)
		errChan <- countErr
	}()

	for i := 0; i < 2; i++ {
		if err := <-errChan; err != nil {
			return nil, err
		}
	}

	// 4. 计算总页数
	var totalPages int64
	if req.Limit > 0 {
		totalPages = (total + req.Limit - 1) / req.Limit
	} else if total > 0 {
		// limit=0 或未提供时，表示获取所有，即只有一页
		totalPages = 1
	}

	// 5. 组装并返回响应数据
	return &ListAdvertisersResponseData{
		Items: advertisers,
		Pagination: &common.Pagination{
			TotalItems:  total,
			TotalPages:  totalPages,
			CurrentPage: req.Page,
			Limit:       req.Limit,
		},
	}, nil
}

// GetByID 获取单个广告主详情
func (s *service) GetByID(ctx context.Context, id string) (*Advertiser, error) {
	if _, err := primitive.ObjectIDFromHex(id); err != nil {
		return nil, ErrInvalidAdvertiserID
	}

	advertiser, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err // 直接传递仓库层的错误
	}
	if advertiser == nil {
		return nil, ErrAdvertiserNotFound
	}
	return advertiser, nil
}

// saveAvatar 保存头像文件并返回其可访问的 URL
func (s *service) saveAvatar(fileHeader *multipart.FileHeader) (string, error) {
	avatarURL, _, err := s.saveAvatarWithPath(fileHeader)
	return avatarURL, err
}

// getAvatarL1L2Path 为avatar生成L1/L2分层路径
func (s *service) getAvatarL1L2Path(createdAt time.Time, filename string) (string, error) {
	// 使用gofile包生成L1/L2路径，使用filename作为ID - 客户头像使用CLG board
	l1, l2, err := levelStore.GetL1L2Separate(createdAt, "CLG", filename)
	if err != nil {
		return "", err
	}

	// 构建L1/L2分层目录结构
	return filepath.Join("CLG", l1, l2, filename), nil
}

// saveAvatarWithPath 保存头像文件并返回其可访问的 URL 和文件路径
func (s *service) saveAvatarWithPath(fileHeader *multipart.FileHeader) (string, string, error) {
	// 1. 生成唯一文件名以避免冲突
	ext := filepath.Ext(fileHeader.Filename)
	uniqueFilename := uuid.New().String() + ext

	// 2. 使用当前时间和文件名生成L1/L2路径
	now := time.Now()
	l1l2Path, err := s.getAvatarL1L2Path(now, uniqueFilename)
	if err != nil {
		logger.Log.Error("生成avatar L1/L2路径失败", logger.Error(err))
		// 回退到原有逻辑
		l1l2Path = uniqueFilename
	}

	// 3. 构建目标存储路径 - 使用media目录而不是static目录
	destPath := filepath.Join(s.cfg.Media.StorageDir, l1l2Path)

	// 4. 确保目标目录存在
	destDir := filepath.Dir(destPath)
	if err := os.MkdirAll(destDir, os.ModePerm); err != nil {
		return "", "", fmt.Errorf("无法创建avatar存储目录: %w", err)
	}

	// 5. 打开上传的文件
	src, err := fileHeader.Open()
	if err != nil {
		return "", "", fmt.Errorf("无法打开上传的文件: %w", err)
	}
	defer src.Close()

	// 6. 创建目标文件
	dst, err := os.Create(destPath)
	if err != nil {
		return "", "", fmt.Errorf("无法创建目标文件: %w", err)
	}
	defer dst.Close()

	// 7. 将上传的文件内容复制到目标文件
	if _, err := io.Copy(dst, src); err != nil {
		return "", "", fmt.Errorf("无法保存文件: %w", err)
	}

	// 8. 构建并返回可访问的 URL 和文件路径
	// 使用media URL而不是static URL，并包含L1/L2路径
	avatarURL := filepath.ToSlash(filepath.Join("/media", l1l2Path))
	return avatarURL, destPath, nil
}

// deleteAvatar removes an avatar file from the filesystem.
func (s *service) deleteAvatar(avatarURL string) error {
	if avatarURL == "" {
		return nil // Nothing to delete
	}

	// 验证URL格式 - 现在avatar使用/media路径
	if !strings.HasPrefix(avatarURL, "/media/") {
		logger.Log.Warn("Invalid avatar URL format", logger.String("url", avatarURL))
		return fmt.Errorf("invalid avatar URL format")
	}

	// 提取相对路径（去掉/media前缀）
	relativePath := strings.TrimPrefix(avatarURL, "/media/")

	// 验证路径不包含危险的路径遍历
	if strings.Contains(relativePath, "..") {
		logger.Log.Warn("Invalid path in avatar URL", logger.String("path", relativePath))
		return fmt.Errorf("invalid path in avatar URL")
	}

	// 构建完整文件路径
	filePath := filepath.Join(s.cfg.Media.StorageDir, relativePath)

	// 确保文件路径在存储目录内
	absStoragePath, _ := filepath.Abs(s.cfg.Media.StorageDir)
	absFilePath, _ := filepath.Abs(filePath)
	if !strings.HasPrefix(absFilePath, absStoragePath) {
		logger.Log.Warn("File path outside storage directory", logger.String("filePath", filePath))
		return fmt.Errorf("file path outside storage directory")
	}

	// Check if the file exists before trying to remove it
	if _, err := os.Stat(filePath); err == nil {
		return os.Remove(filePath)
	}
	return nil
}

// Create 创建一个新的广告主
func (s *service) Create(ctx context.Context, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error) {
	// 1. 检查唯一性和格式
	if req.Email != "" {
		if !common.IsValidEmail(req.Email) {
			return nil, ErrInvalidEmailFormat
		}
		emailExists, err := s.repo.ExistsByEmail(ctx, req.Email)
		if err != nil {
			return nil, err
		}
		if emailExists {
			return nil, ErrEmailExists
		}
	}

	if req.Phone != "" {
		if !common.IsValidPhone(req.Phone) {
			return nil, ErrInvalidPhoneFormat
		}
		phoneExists, err := s.repo.ExistsByPhone(ctx, req.Phone)
		if err != nil {
			return nil, err
		}
		if phoneExists {
			return nil, ErrPhoneExists
		}
	}

	// 处理头像上传
	var avatarURL string
	var savedFilePath string
	var err error
	if avatarFile != nil {
		avatarURL, savedFilePath, err = s.saveAvatarWithPath(avatarFile)
		if err != nil {
			return nil, fmt.Errorf("保存头像失败: %w", err)
		}
	}

	// 2. 创建实体
	advertiser := &Advertiser{
		Name:      req.Name,
		AvatarURL: avatarURL,
		Phone:     req.Phone,
		Email:     req.Email,
		MAppUID:   req.MAppUID,
		Remark:    req.Remark,
		// gomongo会自动添加_ts和_mt字段
	}

	// 3. 保存到数据库
	if err := s.repo.Create(ctx, advertiser); err != nil {
		// 回滚：删除已保存的文件
		if savedFilePath != "" {
			if removeErr := os.Remove(savedFilePath); removeErr != nil {
				logger.Log.Warn("Failed to cleanup avatar file after database error",
					logger.String("filePath", savedFilePath), logger.Error(removeErr))
			}
		}
		return nil, fmt.Errorf("service: 创建广告主失败: %w", err)
	}

	return advertiser, nil
}

// Update 更新一个广告主
func (s *service) Update(ctx context.Context, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error {
	// 0. 验证ID格式
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return ErrInvalidAdvertiserID
	}

	// 1. 获取当前广告主数据
	existingAdvertiser, err := s.repo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return ErrAdvertiserNotFound
		}
		return err
	}

	// 2. 处理头像更新
	var newAvatarURL string
	var newFilePath string
	var oldAvatarURL string

	newAvatarURL = existingAdvertiser.AvatarURL
	if avatarFile != nil {
		// 先保存新文件，但不删除旧文件
		savedURL, savedPath, err := s.saveAvatarWithPath(avatarFile)
		if err != nil {
			return fmt.Errorf("保存新头像失败: %w", err)
		}
		newAvatarURL = savedURL
		newFilePath = savedPath
		oldAvatarURL = existingAdvertiser.AvatarURL
	}

	// 3. 准备更新的数据
	updateData := bson.M{}

	// Use helper to add fields to updateData if they are not nil
	addField := func(key string, value *string) {
		if value != nil {
			updateData[key] = *value
		}
	}

	addField("nm", req.Name)

	if req.Phone != nil {
		if !common.IsValidPhone(*req.Phone) {
			return ErrInvalidPhoneFormat
		}
		phoneExists, err := s.repo.ExistsByPhoneAndNotID(ctx, *req.Phone, id)
		if err != nil {
			return err
		}
		if phoneExists {
			return ErrPhoneExists
		}
		updateData["ph"] = *req.Phone
	}
	if req.Email != nil {
		if !common.IsValidEmail(*req.Email) {
			return ErrInvalidEmailFormat
		}
		emailExists, err := s.repo.ExistsByEmailAndNotID(ctx, *req.Email, id)
		if err != nil {
			return err
		}
		if emailExists {
			return ErrEmailExists
		}
		updateData["em"] = *req.Email
	}
	addField("mUid", req.MAppUID)
	addField("rem", req.Remark)

	// Only update avatar URL if it has changed
	if newAvatarURL != existingAdvertiser.AvatarURL {
		updateData["avatarUrl"] = newAvatarURL
	}

	// 4. 如果有更新，则执行更新（gomongo会自动更新_mt）
	if len(updateData) > 0 {
		// 5. 执行更新
		err = s.repo.Update(ctx, objectID.Hex(), updateData)
		if err != nil {
			// 回滚：删除新保存的文件
			if newFilePath != "" {
				if removeErr := os.Remove(newFilePath); removeErr != nil {
					logger.Log.Warn("Failed to cleanup new avatar file after database error",
						logger.String("filePath", newFilePath), logger.Error(removeErr))
				}
			}
			if errors.Is(err, mongo.ErrNoDocuments) {
				return ErrAdvertiserNotFound
			}
			return fmt.Errorf("service: 更新广告主失败: %w", err)
		}

		// 数据库更新成功后，删除旧文件
		if oldAvatarURL != "" && oldAvatarURL != newAvatarURL {
			if err := s.deleteAvatar(oldAvatarURL); err != nil {
				// Log the error but don't fail the whole update operation
				logger.Log.Warn("删除旧头像失败", logger.String("url", oldAvatarURL), logger.Error(err))
			}
		}
	}

	return nil
}

// Delete a client and merge its videos to another client.
func (s *service) Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error {
	// 1. Validate IDs
	sourceID, err := primitive.ObjectIDFromHex(idToDelete)
	if err != nil {
		return ErrInvalidAdvertiserID
	}
	targetID, err := primitive.ObjectIDFromHex(targetAdvertiserID)
	if err != nil {
		return fmt.Errorf("无效的目标广告主ID: %w", err)
	}

	if sourceID == targetID {
		return ErrMergeToSelf
	}

	// 2. Check if this is the last advertiser
	count, err := s.repo.Count(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("无法统计广告主数量: %w", err)
	}
	if count <= 1 {
		return ErrCannotDeleteLast
	}

	// 3. Check if target advertiser exists
	targetExists, err := s.repo.ExistsByID(ctx, targetAdvertiserID)
	if err != nil {
		return fmt.Errorf("检查目标广告主是否存在时出错: %w", err)
	}
	if !targetExists {
		return ErrTargetAdvertiserNotFound
	}

	// 4. Get source advertiser details to delete avatar later
	sourceAdvertiser, err := s.repo.FindByID(ctx, idToDelete)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return ErrAdvertiserNotFound
		}
		return fmt.Errorf("查找要删除的广告主失败: %w", err)
	}

	// 5. Perform operations sequentially without a transaction
	// Step 5a: Re-assign videos to the target advertiser
	_, err = s.videoRepo.UpdateAdvertiser(ctx, sourceID, targetID)
	if err != nil {
		return fmt.Errorf("转移视频失败: %w", err)
	}

	// Step 5b: Delete the source advertiser
	if err := s.repo.Delete(ctx, idToDelete); err != nil {
		// If this fails, the videos are already safely migrated.
		// The operation can be safely retried by the user.
		return fmt.Errorf("删除广告主失败 (视频已转移): %w", err)
	}

	// If deletion is successful, delete the avatar file from the filesystem
	if sourceAdvertiser.AvatarURL != "" {
		if err := s.deleteAvatar(sourceAdvertiser.AvatarURL); err != nil {
			// Log this error but don't fail the whole operation, as the main DB changes are already committed.
			logger.Log.Warn("删除头像文件失败", logger.String("avatarUrl", sourceAdvertiser.AvatarURL), logger.Error(err))
		}
	}

	return nil
}
