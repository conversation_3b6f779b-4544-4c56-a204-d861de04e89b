package advertiser

import (
	"errors"
	"mime/multipart"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
)

// Handler 处理广告主相关的 HTTP 请求
type Handler struct {
	service Service
	cfg     *config.Config
}

// NewHandler 创建一个新的广告主处理器实例
func NewHandler(service Service, cfg *config.Config) *Handler {
	return &Handler{
		service: service,
		cfg:     cfg,
	}
}

// --- Response Helpers ---
// 注意：这些函数已被弃用，请使用 common.SendSuccess 和 common.SendError

// --- Handlers ---

// List 获取广告主列表
// @Summary 获取广告主列表
// @Description 获取广告主列表，支持分页和模糊搜索
// @Tags advertisers
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Param name query string false "按名称模糊搜索"
// @Param phone query string false "按电话精确搜索"
// @Param email query string false "按邮箱精确搜索"
// @Success 200 {object} common.APIResponse{data=ListAdvertisersResponseData}
// @Failure 400 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/advertisers [get]
func (h *Handler) List(c *gin.Context) {
	var req ListAdvertisersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		common.SendError(c, http.StatusBadRequest, "无效的查询参数", err.Error())
		return
	}

	result, err := h.service.List(c.Request.Context(), req)
	if err != nil {
		common.SendError(c, http.StatusInternalServerError, "获取广告主列表失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, result)
}

// GetByID 获取单个广告主详情
// @Summary 获取单个广告主详情
// @Description 根据ID获取单个广告主的详细信息
// @Tags advertisers
// @Accept json
// @Produce json
// @Param id path string true "广告主ID"
// @Success 200 {object} common.APIResponse{data=Advertiser}
// @Failure 400 {object} common.APIResponse
// @Failure 404 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/advertisers/{id} [get]
func (h *Handler) GetByID(c *gin.Context) {
	id := c.Param("id")

	advertiser, err := h.service.GetByID(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, ErrInvalidAdvertiserID) {
			common.SendError(c, http.StatusBadRequest, "无效的客户ID", err.Error())
			return
		}
		if errors.Is(err, ErrAdvertiserNotFound) {
			common.SendError(c, http.StatusNotFound, "找不到指定的客户", err.Error())
			return
		}
		common.SendError(c, http.StatusInternalServerError, "获取客户信息失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, advertiser)
}

// Create 创建一个新广告主
// @Summary 创建广告主
// @Description 创建一个新的广告主，支持上传头像
// @Tags advertisers
// @Accept multipart/form-data
// @Produce json
// @Param avatar formData file false "广告主头像文件"
// @Param nm formData string true "广告主名称"
// @Param ph formData string false "电话号码"
// @Param em formData string false "电子邮箱"
// @Param mUid formData string false "主App用户ID"
// @Param rem formData string false "备注信息"
// @Success 201 {object} common.APIResponse{data=Advertiser}
// @Failure 400 {object} common.APIResponse
// @Failure 409 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/advertisers [post]
func (h *Handler) Create(c *gin.Context) {
	var req CreateAdvertiserRequest
	// 手动从表单绑定数据
	req.Name = c.PostForm("nm")
	req.Phone = c.PostForm("ph")
	req.Email = c.PostForm("em")
	req.MAppUID = c.PostForm("mUid")
	req.Remark = c.PostForm("rem")

	// 验证必要字段
	if req.Name == "" {
		common.SendError(c, http.StatusBadRequest, "广告主名称(nm)是必填项", "name field is required")
		return
	}

	// 获取上传的文件
	var avatarFile *multipart.FileHeader
	file, err := c.FormFile("avatar")
	if err != nil && err != http.ErrMissingFile {
		common.SendError(c, http.StatusBadRequest, "处理上传文件时出错", err.Error())
		return
	}
	avatarFile = file

	advertiser, err := h.service.Create(c.Request.Context(), req, avatarFile)
	if err != nil {
		if errors.Is(err, ErrInvalidEmailFormat) || errors.Is(err, ErrInvalidPhoneFormat) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrEmailExists) || errors.Is(err, ErrPhoneExists) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		// 检查 MongoDB 唯一键冲突
		var writeException mongo.WriteException
		if errors.As(err, &writeException) {
			for _, writeError := range writeException.WriteErrors {
				if writeError.Code == 11000 {
					// Check which field caused the duplicate key error
					if strings.Contains(writeError.Message, "nm_1") {
						common.SendError(c, http.StatusConflict, "客户名称已存在", err.Error())
					} else {
						common.SendError(c, http.StatusConflict, "存在重复的字段", err.Error())
					}
					return
				}
			}
		}

		common.SendError(c, http.StatusInternalServerError, "创建客户失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusCreated, advertiser, "创建成功")
}

// Update 更新一个广告主
// @Summary 更新广告主
// @Description 更新一个已存在的广告主信息，支持部分更新和头像上传
// @Tags advertisers
// @Accept multipart/form-data
// @Produce json
// @Param id path string true "广告主ID"
// @Param avatar formData file false "新的广告主头像文件"
// @Param nm formData string false "广告主名称"
// @Param ph formData string false "电话号码"
// @Param em formData string false "电子邮箱"
// @Param mUid formData string false "主App用户ID"
// @Param rem formData string false "备注信息"
// @Success 200 {object} common.APIResponse
// @Failure 400 {object} common.APIResponse
// @Failure 404 {object} common.APIResponse
// @Failure 409 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/advertisers/{id} [patch]
func (h *Handler) Update(c *gin.Context) {
	id := c.Param("id")

	var req UpdateAdvertiserRequest
	// Manually populate request from form data, as fields are optional
	if name, ok := c.GetPostForm("nm"); ok {
		req.Name = &name
	}
	if phone, ok := c.GetPostForm("ph"); ok {
		req.Phone = &phone
	}
	if email, ok := c.GetPostForm("em"); ok {
		req.Email = &email
	}
	if mUid, ok := c.GetPostForm("mUid"); ok {
		req.MAppUID = &mUid
	}
	if remark, ok := c.GetPostForm("rem"); ok {
		req.Remark = &remark
	}

	// Handle file upload
	avatarFile, err := c.FormFile("avatar")
	if err != nil && err != http.ErrMissingFile {
		common.SendError(c, http.StatusBadRequest, "处理上传文件时出错", err.Error())
		return
	}

	err = h.service.Update(c.Request.Context(), id, req, avatarFile)
	if err != nil {
		if errors.Is(err, ErrInvalidAdvertiserID) {
			common.SendError(c, http.StatusBadRequest, "无效的客户ID", err.Error())
			return
		}
		if strings.Contains(err.Error(), "没有提供需要更新的信息") {
			common.SendError(c, http.StatusBadRequest, "没有提供需要更新的信息", err.Error())
			return
		}
		if errors.Is(err, ErrInvalidEmailFormat) || errors.Is(err, ErrInvalidPhoneFormat) {
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
			return
		}
		if errors.Is(err, ErrAdvertiserNotFound) {
			common.SendError(c, http.StatusNotFound, "找不到要更新的客户", err.Error())
			return
		}
		if errors.Is(err, ErrEmailExists) || errors.Is(err, ErrPhoneExists) {
			common.SendError(c, http.StatusConflict, err.Error(), err.Error())
			return
		}
		var writeException mongo.WriteException
		if errors.As(err, &writeException) {
			for _, writeError := range writeException.WriteErrors {
				if writeError.Code == 11000 {
					if strings.Contains(writeError.Message, "nm_1") {
						common.SendError(c, http.StatusConflict, "客户名称已存在", err.Error())
					} else if strings.Contains(writeError.Message, "em_1") {
						common.SendError(c, http.StatusConflict, "电子邮箱已被使用", err.Error())
					} else if strings.Contains(writeError.Message, "ph_1") {
						common.SendError(c, http.StatusConflict, "电话号码已被使用", err.Error())
					} else {
						common.SendError(c, http.StatusConflict, "存在重复的字段", err.Error())
					}
					return
				}
			}
		}

		common.SendError(c, http.StatusInternalServerError, "更新客户失败", err.Error())
		return
	}

	common.SendSuccess(c, http.StatusOK, nil, "更新成功")
}

// Delete 删除一个广告主，并将其视频合并到另一个广告主
// @Summary 合并并删除广告主
// @Description 将一个广告主的所有视频转移到另一个广告主后，删除该广告主。如果系统中只有一个广告主，则此操作会失败。
// @Tags advertisers
// @Accept json
// @Produce json
// @Param id path string true "要删除的广告主ID"
// @Param mergeRequest body MergeDeleteAdvertiserRequest true "合并目标"
// @Success 200 {object} common.APIResponse
// @Failure 400 {object} common.APIResponse
// @Failure 404 {object} common.APIResponse
// @Failure 500 {object} common.APIResponse
// @Router /api/advertisers/{id} [delete]
func (h *Handler) Delete(c *gin.Context) {
	id := c.Param("id")
	var req MergeDeleteAdvertiserRequest
	// It's a DELETE request, so the body might be absent
	if err := c.ShouldBindJSON(&req); err != nil && err.Error() != "EOF" {
		common.SendError(c, http.StatusBadRequest, "无效的请求体", err.Error())
		return
	}

	err := h.service.Delete(c.Request.Context(), id, req.TargetAdvertiserID)
	if err != nil {
		switch {
		case errors.Is(err, ErrInvalidAdvertiserID):
			common.SendError(c, http.StatusBadRequest, "无效的客户ID", err.Error())
		case errors.Is(err, ErrAdvertiserNotFound):
			common.SendError(c, http.StatusNotFound, "找不到要删除的客户", err.Error())
		case errors.Is(err, ErrCannotDeleteLast):
			common.SendError(c, http.StatusBadRequest, err.Error(), err.Error())
		case errors.Is(err, ErrTargetAdvertiserNotFound):
			common.SendError(c, http.StatusBadRequest, "指定的目标客户不存在", err.Error())
		case errors.Is(err, ErrMergeToSelf):
			common.SendError(c, http.StatusBadRequest, "不能将客户合并到其自身", err.Error())
		case strings.Contains(err.Error(), "must provide a target advertiser ID"):
			common.SendError(c, http.StatusBadRequest, "此客户有关联的视频，请指定一个新客户来接收这些视频", err.Error())
		default:
			common.SendError(c, http.StatusInternalServerError, "删除客户失败", err.Error())
		}
		return
	}

	common.SendSuccess(c, http.StatusOK, nil, "删除成功")
}
