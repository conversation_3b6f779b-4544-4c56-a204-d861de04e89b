package common

import (
	"regexp"
)

// IsValidEmail 检查邮箱格式是否有效
func IsValidEmail(email string) bool {
	// 一个常用的邮箱正则表达式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// IsValidPhone 检查电话号码格式是否为10位数字
func IsValidPhone(phone string) bool {
	// 正则表达式，只匹配10位数字
	phoneRegex := regexp.MustCompile(`^\d{10}$`)
	return phoneRegex.MatchString(phone)
} 