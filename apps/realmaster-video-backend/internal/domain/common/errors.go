package common

import "errors"

// 通用错误类型
var (
	// 验证错误
	ErrInvalidRequest     = errors.New("无效的请求参数")
	ErrInvalidID          = errors.New("无效的ID格式")
	ErrInvalidCategoryID  = errors.New("无效的分类ID格式")
	ErrInvalidClientID    = errors.New("无效的客户ID格式")
	ErrInvalidAdvertiserID = errors.New("无效的广告主ID格式")
	ErrInvalidTimeFormat  = errors.New("无效的时间格式")
	ErrInvalidFileFormat  = errors.New("无效的文件格式")
	
	// 资源错误
	ErrResourceNotFound   = errors.New("资源未找到")
	ErrResourceExists     = errors.New("资源已存在")
	ErrResourceInUse      = errors.New("资源正在使用中")
	
	// 权限错误
	ErrUnauthorized       = errors.New("未授权访问")
	ErrForbidden          = errors.New("禁止访问")
	
	// 业务逻辑错误
	ErrInvalidOperation   = errors.New("无效的操作")
	ErrOperationFailed    = errors.New("操作失败")
	ErrInvalidStatus      = errors.New("无效的状态")
	
	// 文件操作错误
	ErrFileNotFound       = errors.New("文件未找到")
	ErrFileUploadFailed   = errors.New("文件上传失败")
	ErrFileSaveFailed     = errors.New("文件保存失败")
	ErrFileDeleteFailed   = errors.New("文件删除失败")
	
	// 数据库错误
	ErrDatabaseOperation  = errors.New("数据库操作失败")
	ErrDuplicateKey       = errors.New("重复的键值")
	
	// 外部服务错误
	ErrExternalService    = errors.New("外部服务错误")
	ErrNetworkTimeout     = errors.New("网络超时")
)

// ErrorType 错误类型枚举
type ErrorType string

const (
	ErrorTypeValidation   ErrorType = "validation"
	ErrorTypeNotFound     ErrorType = "not_found"
	ErrorTypeConflict     ErrorType = "conflict"
	ErrorTypeInternal     ErrorType = "internal"
	ErrorTypeExternal     ErrorType = "external"
	ErrorTypeUnauthorized ErrorType = "unauthorized"
	ErrorTypeForbidden    ErrorType = "forbidden"
)

// AppError 应用程序错误结构
type AppError struct {
	Type    ErrorType `json:"type"`
	Code    string    `json:"code"`
	Message string    `json:"message"`
	Details string    `json:"details,omitempty"`
	Err     error     `json:"-"`
}

func (e *AppError) Error() string {
	if e.Err != nil {
		return e.Message + ": " + e.Err.Error()
	}
	return e.Message
}

func (e *AppError) Unwrap() error {
	return e.Err
}

// NewValidationError 创建验证错误
func NewValidationError(code, message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeValidation,
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// NewNotFoundError 创建资源未找到错误
func NewNotFoundError(code, message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeNotFound,
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// NewConflictError 创建冲突错误
func NewConflictError(code, message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeConflict,
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// NewInternalError 创建内部错误
func NewInternalError(code, message string, err error) *AppError {
	return &AppError{
		Type:    ErrorTypeInternal,
		Code:    code,
		Message: message,
		Err:     err,
	}
}
