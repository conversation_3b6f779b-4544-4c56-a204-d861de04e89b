package common

import (
	"github.com/gin-gonic/gin"

	"realmaster-video-backend/internal/platform/logger"
	// zap兼容别名
)

// SendSuccess sends a standardized success response.
func SendSuccess(c *gin.Context, status int, data interface{}, msg ...string) {
	message := "操作成功"
	if len(msg) > 0 {
		message = msg[0]
	}
	c.JSO<PERSON>(status, APIResponse{
		OK:   1,
		Msg:  message,
		Data: data,
	})
}

// SendError sends a standardized error response and logs the detailed error.
func SendError(c *gin.Context, status int, errMessage, devMessage string) {
	logger.Log.Error(devMessage, logger.String("error", errMessage))
	c.AbortWithStatusJSON(status, APIResponse{
		OK:  0,
		Err: errMessage,
	})
}

// SendAppError 发送应用程序错误响应
func SendAppError(c *gin.Context, appErr *AppError) {
	var status int
	switch appErr.Type {
	case ErrorTypeValidation:
		status = 400
	case ErrorTypeNotFound:
		status = 404
	case ErrorTypeConflict:
		status = 409
	case ErrorTypeUnauthorized:
		status = 401
	case ErrorTypeForbidden:
		status = 403
	case ErrorTypeExternal:
		status = 502
	default:
		status = 500
	}

	logger.Log.Error("应用程序错误",
		logger.String("type", string(appErr.Type)),
		logger.String("code", appErr.Code),
		logger.String("message", appErr.Message),
		logger.String("details", appErr.Details),
	)

	c.AbortWithStatusJSON(status, APIResponse{
		OK:  0,
		Err: appErr.Message,
	})
}
