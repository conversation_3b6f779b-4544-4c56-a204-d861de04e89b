package common

import (
	"os"
	"path/filepath"
	"strings"

	"realmaster-video-backend/internal/platform/logger"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// IsValidObjectID 检查字符串是否为有效的MongoDB ObjectID
func IsValidObjectID(id string) bool {
	_, err := primitive.ObjectIDFromHex(id)
	return err == nil
}

// CleanupEmptyDirectories 递归清理空目录
// 从给定路径开始向上清理，直到遇到非空目录或到达根目录
func CleanupEmptyDirectories(dirPath string, rootDir string) {
	// 确保不会删除根目录本身
	if dirPath == rootDir || !strings.HasPrefix(dirPath, rootDir) {
		return
	}

	// 检查目录是否为空
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		logger.Log.Debug("无法读取目录进行清理", logger.String("path", dirPath), logger.Error(err))
		return
	}

	// 如果目录为空，删除它并继续向上清理
	if len(entries) == 0 {
		if err := os.Remove(dirPath); err != nil {
			logger.Log.Debug("无法删除空目录", logger.String("path", dirPath), logger.Error(err))
			return
		}
		logger.Log.Info("已清理空目录", logger.String("path", dirPath))

		// 递归清理父目录
		parentDir := filepath.Dir(dirPath)
		CleanupEmptyDirectories(parentDir, rootDir)
	}
}
