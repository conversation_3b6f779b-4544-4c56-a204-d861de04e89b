package category

import (
	"context"
	"errors"
	"fmt"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/domain/video"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	NoneCategoryName = "None"
)

var (
	ErrCategoryNotFound         = errors.New("分类不存在")
	ErrInvalidCategoryID        = errors.New("无效的分类ID")
	ErrCategoryNameExists       = errors.New("分类名已存在")
	ErrCannotDeleteNoneCategory = errors.New("不能删除 'None' 分类")
	ErrCategoryNameRequired     = errors.New("分类名称不能为空")
	ErrCategoryNameTooLong      = errors.New("分类名称不能超过50个字符")
	ErrUpdatePayloadRequired    = errors.New("没有提供需要更新的信息")
)

// Service 定义分类相关的业务逻辑接口
type Service interface {
	Create(ctx context.Context, req CreateCategoryRequest) (*Category, error)
	List(ctx context.Context, req ListCategoriesRequest) (*ListCategoriesResponseData, error)
	Delete(ctx context.Context, id string) error
	Update(ctx context.Context, id string, req UpdateCategoryRequest) error
	EnsureNoneCategoryExists(ctx context.Context) error
}

// service 实现了 Service 接口
type service struct {
	repo      Repository
	videoRepo video.Repository
}

// NewService 创建一个新的分类服务实例
func NewService(repo Repository, videoRepo video.Repository) Service {
	return &service{
		repo:      repo,
		videoRepo: videoRepo,
	}
}

// EnsureNoneCategoryExists 检查并确保 "None" 分类存在
func (s *service) EnsureNoneCategoryExists(ctx context.Context) error {
	_, err := s.repo.FindByName(ctx, NoneCategoryName)
	if err == nil {
		// "None" 分类已存在
		return nil
	}

	if errors.Is(err, ErrCategoryNotFound) {
		// 不存在，则创建
		noneCategory := &Category{
			Name:  NoneCategoryName,
			Order: 9999, // 确保 "None" 分类排在最后
			// gomongo会自动添加_ts和_mt字段
		}
		return s.repo.Create(ctx, noneCategory)
	}

	// 其他错误
	return fmt.Errorf("检查 'None' 分类时出错: %w", err)
}

// List 获取所有分类列表
func (s *service) List(ctx context.Context, req ListCategoriesRequest) (*ListCategoriesResponseData, error) {
	// 1. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10 // 默认每页10条
	}

	// 2. 对于分类列表，filter暂时为空
	filter := bson.M{}

	// 3. 并行执行查询和计数
	var categories []Category
	var total int64
	var findErr, countErr error

	errChan := make(chan error, 2)
	go func() {
		categories, findErr = s.repo.FindAll(ctx, req.Page, req.Limit)
		errChan <- findErr
	}()
	go func() {
		total, countErr = s.repo.Count(ctx, filter)
		errChan <- countErr
	}()

	for i := 0; i < 2; i++ {
		if err := <-errChan; err != nil {
			return nil, err
		}
	}

	// 4. 计算总页数
	totalPages := (total + req.Limit - 1) / req.Limit

	// 5. 组装并返回响应数据
	return &ListCategoriesResponseData{
		Items: categories,
		Pagination: &common.Pagination{
			TotalItems:  total,
			TotalPages:  totalPages,
			CurrentPage: req.Page,
			Limit:       req.Limit,
		},
	}, nil
}

// Create 创建新的分类
func (s *service) Create(ctx context.Context, req CreateCategoryRequest) (*Category, error) {
	// 1. 验证请求数据
	if req.Name == "" {
		return nil, ErrCategoryNameRequired
	}
	if len(req.Name) > 50 {
		return nil, ErrCategoryNameTooLong
	}

	// 2. 检查分类名是否已存在 (此逻辑已由数据库唯一索引保证，但可在service层提前拦截以返回更具体错误)
	// exists, err := s.repo.ExistsByName(ctx, req.Name)
	// if err != nil {
	// 	return nil, fmt.Errorf("检查分类名失败: %w", err)
	// }
	// if exists {
	// 	return nil, ErrCategoryNameExists
	// }

	// 3. 创建分类对象
	category := &Category{
		Name:  req.Name,
		Order: 0, // 默认排序值为0
		// gomongo会自动添加_ts和_mt字段
	}

	// 如果请求中提供了排序值，则使用请求中的值
	if req.Order != nil {
		category.Order = *req.Order
	}

	// 4. 保存到数据库
	if err := s.repo.Create(ctx, category); err != nil {
		// 这里虽然有数据库唯一索引，但让它返回一个通用错误，
		// 在handler层可以捕获具体的mongo write exception
		return nil, fmt.Errorf("创建分类失败: %w", err)
	}

	return category, nil
}

// Delete 删除指定ID的分类，并将其下的视频转移到 "None" 分类
func (s *service) Delete(ctx context.Context, id string) error {
	// 1. 验证ID格式
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return ErrInvalidCategoryID
	}

	// 2. 查找 "None" 分类
	noneCategory, err := s.repo.FindByName(ctx, NoneCategoryName)
	if err != nil {
		return fmt.Errorf("查找 'None' 分类失败: %w", err)
	}

	// 3. 检查是否在删除 "None" 分类本身
	if objectID == noneCategory.ID {
		return ErrCannotDeleteNoneCategory
	}

	// 4. 将视频从旧分类转移到 "None" 分类
	err = s.videoRepo.UpdateCategory(ctx, objectID, noneCategory.ID)
	if err != nil {
		return fmt.Errorf("更新视频分类失败: %w", err)
	}

	// 5. 删除旧分类
	err = s.repo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("删除分类失败: %w", err)
	}

	return nil
}

// Update 更新分类信息
func (s *service) Update(ctx context.Context, id string, req UpdateCategoryRequest) error {
	// 0. 验证ID格式
	if _, err := primitive.ObjectIDFromHex(id); err != nil {
		return ErrInvalidCategoryID
	}

	// 1. 检查是否有需要更新的字段
	if req.Name == nil && req.Order == nil {
		return ErrUpdatePayloadRequired
	}

	// 2. 检查分类是否存在
	exists, err := s.repo.ExistsByID(ctx, id)
	if err != nil {
		return fmt.Errorf("检查分类是否存在失败: %w", err)
	}
	if !exists {
		return ErrCategoryNotFound
	}

	updateData := primitive.M{}

	// 3. 处理要更新的字段
	if req.Name != nil {
		name := *req.Name
		// 3.1 验证字段
		if name == "" {
			return ErrCategoryNameRequired
		}
		if len(name) > 50 {
			return ErrCategoryNameTooLong
		}
		// 3.2 检查新名称是否已被其他分类占用
		nameExists, err := s.repo.ExistsByNameAndNotID(ctx, name, id)
		if err != nil {
			return fmt.Errorf("检查新分类名失败: %w", err)
		}
		if nameExists {
			return ErrCategoryNameExists
		}
		updateData["nm"] = name
	}

	if req.Order != nil {
		updateData["ord"] = *req.Order
	}

	// 4. 执行更新（gomongo会自动更新_mt）
	return s.repo.Update(ctx, id, updateData)
}
