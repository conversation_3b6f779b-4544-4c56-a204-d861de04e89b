package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用程序配置结构体
type Config struct {
	Server struct {
		Port     int    `mapstructure:"port"`
		Host     string `mapstructure:"host"`
		DraftDir string `mapstructure:"draft_dir"`
	} `mapstructure:"server"`

	// golog配置
	Golog struct {
		Dir     string `mapstructure:"dir"`
		Level   string `mapstructure:"level"`
		Verbose string `mapstructure:"verbose"`
		Info    string `mapstructure:"info"`
		Error   string `mapstructure:"error"`
		Format  string `mapstructure:"format"`
	} `mapstructure:"golog"`

	Media struct {
		ServerURL  string `mapstructure:"server_url"`
		StorageDir string `mapstructure:"storage_dir"`
	} `mapstructure:"media"`

	Auth struct {
		JWTSecret         string `mapstructure:"jwtSecret"`
		DevMode           bool   `mapstructure:"devMode"`
		DevJWTExpireHours int    `mapstructure:"devJWTExpireHours"`
	} `mapstructure:"auth"`

	Transaction struct {
		Support bool `mapstructure:"support"`
	} `mapstructure:"transaction"`

	// 保留用于兼容性，但gomongo会直接从TOML读取数据库配置
	MongoDB struct {
		URI      string `mapstructure:"uri"`
		Database string `mapstructure:"database"`
	} `mapstructure:"mongodb"`
}

// LoadConfig 从配置文件或环境变量加载配置
func LoadConfig() (*Config, error) {
	v := viper.New()

	// 设置默认值
	v.SetDefault("server.port", 8080)
	v.SetDefault("server.host", "0.0.0.0")
	v.SetDefault("server.draft_dir", "/var/www/draft/rm_video_uploads")
	v.SetDefault("transaction.support", false) // 默认单机环境
	v.SetDefault("media.server_url", "http://localhost:9000/videos")
	v.SetDefault("media.storage_dir", "/var/www/rm_video_media")
	v.SetDefault("static_files.storage_path", "/var/www/static")
	v.SetDefault("static_files.base_url", "/static")

	// golog默认值
	v.SetDefault("golog.dir", "/var/log/realmaster_video")
	v.SetDefault("golog.level", "info")
	v.SetDefault("golog.verbose", "verbose.log")
	v.SetDefault("golog.info", "info.log")
	v.SetDefault("golog.error", "error.log")
	v.SetDefault("golog.format", "json")

	// 设置配置文件名称和类型
	v.SetConfigName("config")
	v.SetConfigType("toml") // 改为TOML格式
	v.AddConfigPath(".")
	v.AddConfigPath("./config")

	// 设置环境变量前缀
	v.SetEnvPrefix("RM")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	return &config, nil
}
